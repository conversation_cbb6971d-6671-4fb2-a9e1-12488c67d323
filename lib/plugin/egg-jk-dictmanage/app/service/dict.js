'use strict';

const Service = require('egg').Service;

class DictService extends Service {
  /**
   * 获取字典类型列表
   * @param {Object} params - 查询参数
   * @return {Object} 字典类型列表和总数
   */
  async find(params) {
    const { ctx } = this;
    const { page = 1, limit = 10, name, key } = params;
    const skip = (Number(page) - 1) * Number(limit);

    // 构建查询条件
    const query = {};
    if (name) {
      query.name = new RegExp(name, 'i');
    }
    if (key) {
      query.key = new RegExp(key, 'i');
    }

    // 查询数据
    const count = await ctx.model.DictType.countDocuments(query);
    const list = await ctx.model.DictType.find(query)
      .sort({ updatedAt: -1 })
      .skip(skip)
      .limit(Number(limit));
    return {
      list,
      count,
      page: Number(page),
      limit: Number(limit),
    };
  }

  // 获取字典类型列表
  async findAll(params) {
    const { ctx } = this;
    const { name, key } = params;
    const query = {
      enabled: true,
    };
    if (name) {
      query.name = new RegExp(name, 'i');
    }
    if (key) {
      query.key = new RegExp(key, 'i');
    }
    return await ctx.model.DictType.find(query).sort({ updatedAt: -1 }).select('name key remark -_id');
  }

  /**
   * 获取字典类型详情
   * @param {String} id - 字典类型ID
   * @return {Object} 字典类型详情
   */
  async findById(id) {
    const { ctx } = this;
    return await ctx.model.DictType.findById(id);
  }

  /**
   * 创建字典类型
   * @param {Object} payload - 字典类型数据
   * @return {Object} 创建结果
   */
  async create(payload) {
    const { ctx } = this;
    // 检查key是否已存在
    const existDict = await ctx.model.DictType.findOne({ key: payload.key });
    if (existDict) {
      ctx.throw(400, '字典标识已存在');
    }
    return await ctx.model.DictType.create(payload);
  }

  /**
   * 更新字典类型
   * @param {String} id - 字典类型ID
   * @param {Object} payload - 字典类型数据
   * @return {Object} 更新结果
   */
  async update(id, payload) {
    const { ctx } = this;
    // 检查key是否已存在（排除自身）
    if (payload.key) {
      const existDict = await ctx.model.DictType.findOne({
        key: payload.key,
        _id: { $ne: id },
      });
      if (existDict) {
        ctx.throw(400, '字典标识已存在');
      }
    }
    return await ctx.model.DictType.findByIdAndUpdate(id, payload, { new: true });
  }

  /**
   * 删除字典类型
   * @param {String} id - 字典类型ID
   * @param ids
   * @return {Object} 删除结果
   */
  async destroy(id, ids) {
    const { ctx } = this;
    if (ids) {
      return this.batchDestroy(ids);
    }
    // 检查是否有字典项
    const itemCount = await ctx.model.DictInfo.countDocuments({ typeId: id });
    if (itemCount > 0) {
      ctx.throw(400, '该字典类型下有字典项，无法删除');
    }
    return await ctx.model.DictType.findByIdAndRemove(id);
  }

  /**
   * 批量删除字典类型
   * @param {Array} ids - 字典类型ID数组
   * @return {Object} 删除结果
   */
  async batchDestroy(ids) {
    const { ctx } = this;
    // 检查是否有字典项
    for (const id of ids) {
      const itemCount = await ctx.model.DictInfo.countDocuments({ typeId: id });
      if (itemCount > 0) {
        ctx.throw(400, `ID为${id}的字典类型下有字典项，无法删除`);
      }
    }
    return await ctx.model.DictType.deleteMany({ _id: { $in: ids } });
  }

  /**
   * 获取字典项列表
   * @param {Object} params - 查询参数
   * @return {Object} 字典项列表和总数
   */
  async findInfo(params) {
    const { ctx } = this;
    const { page = 1, limit = 10, typeId, parentId = null } = params;
    const skip = (Number(page) - 1) * Number(limit);

    // 构建查询条件
    const query = { typeId };
    if (parentId === null) {
      query.parentId = null;
    } else {
      query.parentId = parentId;
    }

    // 查询数据
    const count = await ctx.model.DictInfo.countDocuments(query);
    const list = await ctx.model.DictInfo.find(query)
      .sort({ orderNum: 1, updatedAt: -1 })
      .skip(skip)
      .limit(Number(limit));
    return {
      list,
      count,
      page: Number(page),
      limit: Number(limit),
    };
  }

  /**
   * 获取字典项详情
   * @param {String} id - 字典项ID
   * @return {Object} 字典项详情
   */
  async findInfoById(id) {
    const { ctx } = this;
    return await ctx.model.DictInfo.findById(id);
  }

  /**
   * 创建字典项
   * @param {Object} payload - 字典项数据
   * @return {Object} 创建结果
   */
  async createInfo(payload) {
    const { ctx } = this;
    try {
      // 检查字典编码在同一类型下是否已存在
      const existItem = await ctx.model.DictInfo.findOne({
        typeId: payload.typeId,
        value: payload.value,
      });

      if (existItem) {
        throw new Error('字典编码在当前类型下已存在');
      }

      // 如果有父级，检查父级是否存在且类型是否匹配
      if (payload.parentId) {
        const parent = await ctx.model.DictInfo.findById(payload.parentId);
        if (!parent) {
          ctx.throw(400, '父级字典项不存在');
        }
        if (parent.typeId !== payload.typeId) {
          ctx.throw(400, '父级字典项类型不匹配');
        }
      }
      const result = await ctx.model.DictInfo.create(payload);
      // 创建后清除缓存
      if (result) {
        await this.clearDictCache(result.typeId);
      }
      return result;
    } catch (err) {
      ctx.logger.error(err);
      throw err;
    }
  }

  /**
   * 更新字典项
   * @param {String} id - 字典项ID
   * @param {Object} payload - 字典项数据
   * @return {Object} 更新结果
   */
  async updateInfo(id, payload) {
    const { ctx } = this;
    try {
      const result = await ctx.model.DictInfo.findByIdAndUpdate(id, payload, { new: true });
      // 更新后清除缓存
      if (result) {
        await this.clearDictCache(result.typeId);
      }
      return result;
    } catch (err) {
      ctx.logger.error(err);
      throw err;
    }
  }

  /**
   * 删除字典项
   * @param {String} id - 字典项ID
   * @return {Object} 删除结果
   */
  async destroyInfo(id) {
    const { ctx } = this;
    const dictInfo = await ctx.model.DictInfo.findById(id);
    if (dictInfo) {
      // 检查是否有子项
      const childCount = await ctx.model.DictInfo.countDocuments({ parentId: id });
      if (childCount > 0) {
        ctx.throw(400, '该字典项下有子项，无法删除');
      }
      const result = await ctx.model.DictInfo.findByIdAndRemove(id);
      // 删除后清除缓存
      await this.clearDictCache(dictInfo.typeId);
      return result;
    }
    return null;
  }

  /**
   * 批量删除字典项
   * @param {Array} ids - 字典项ID数组
   * @return {Object} 删除结果
   */
  async batchDestroyInfo(ids) {
    const { ctx } = this;
    // 检查是否有子项
    for (const id of ids) {
      const childCount = await ctx.model.DictInfo.countDocuments({ parentId: id });
      if (childCount > 0) {
        ctx.throw(400, `ID为${id}的字典项下有子项，无法删除`);
      }
    }
    return await ctx.model.DictInfo.deleteMany({ _id: { $in: ids } });
  }

  /**
   * 获取字典项树形结构
   * @param {String} typeId - 字典类型ID
   * @param {String} parentId - 父级ID
   * @return {Array} 字典项树形结构
   */
  async getTree(typeId, parentId = null) {
    const { ctx } = this;
    const list = await ctx.model.DictInfo.find({ typeId, parentId })
      .sort({ orderNum: 1, updatedAt: -1 });

    // 为每个节点查询是否有子节点
    const result = await Promise.all(list.map(async item => {
      const hasChildren = await ctx.model.DictInfo.exists({ parentId: item._id });
      return {
        ...item.toObject(),
        hasChildren: !!hasChildren,
      };
    }));

    return result;
  }

  /**
   * 根据字典类型key获取字典项列表
   * @param {string} key - 字典类型标识
   */
  async getItemsByKey(key) {
    const { ctx } = this;
    // 先查找字典类型
    const dictType = await ctx.model.DictType.findOne({ key, enabled: true, parentId: null });
    if (!dictType) {
      throw new Error('字典类型不存在或已禁用');
    }
    // 查找该类型下的所有启用的字典项
    const dictItems = await ctx.model.DictInfo.find({
      typeId: dictType._id,
      enabled: true,
    }).select('name value -_id').sort({ orderNum: 1 });

    return dictItems;
  }

  /**
   * 根据字典类型和父级值获取子项
   * @param {string} key - 字典类型key
   * @param {string} parentValue - 父级值
   * @return {Promise<Array>} 子项列表
   */
  async getChildrenByParent(key, parentValue) {
    const { ctx } = this;
    try {
      // 先根据key查找字典类型
      const dictType = await ctx.model.DictType.findOne({
        key,
        enabled: true,
      });

      if (!dictType) {
        throw new Error('字典类型不存在或已禁用');
      }

      // 查询条件
      const query = {
        typeId: dictType._id,
        enabled: true,
      };

      // 如果parentValue为root，表示查询顶级节点
      if (parentValue === 'root') {
        query.parentId = null;
      } else {
        // 先查找父级记录
        const parentItem = await ctx.model.DictInfo.findOne({
          typeId: dictType._id,
          value: parentValue,
          enabled: true,
        });
        if (!parentItem) {
          throw new Error('父级字典项不存在');
        }
        query.parentId = parentItem._id;
      }

      // 查询子项列表
      const list = await ctx.model.DictInfo.find(query)
        .select('_id name value parentId')
        .sort({ orderNum: 1 })
        .lean();

      // 转换为级联选择器需要的格式
      return list.map(item => ({
        value: item.value,
        label: item.name,
        leaf: false, // 默认都不是叶子节点，让其可以继续点击加载
      }));
    } catch (err) {
      ctx.logger.error('获取字典子项失败:', err);
      throw err;
    }
  }

  /**
   * 获取字典值的层级链
   * @param {Object} params - 参数对象
   * @param {string} params.key - 字典类型key
   * @param {string} params.value - 当前值
   * @return {Promise<Array>} 返回级联选择器所需的数据格式
   */
  async getValueChain(params) {
    const { ctx } = this;
    try {
      const { key, value } = params;

      // 1. 查询字典类型
      const dictType = await ctx.model.DictType.findOne({
        key,
        enabled: true,
      });

      if (!dictType) {
        ctx.logger.error('字典类型不存在或已禁用:', key);
        return [];
      }

      // 2. 获取目标字典项
      const targetItem = await ctx.model.DictInfo.findOne({
        typeId: dictType._id,
        value,
        enabled: true,
      }).lean();

      if (!targetItem) {
        ctx.logger.error('字典项不存在或已禁用:', value);
        return [];
      }

      // 3. 获取父级链路
      const parentChain = [];
      let currentId = targetItem.parentId;

      // 从下往上查找父级
      while (currentId) {
        const parent = await ctx.model.DictInfo.findOne({
          _id: currentId,
          enabled: true,
        }).lean();

        if (!parent) break;

        parentChain.unshift(parent);
        currentId = parent.parentId;
      }

      // 将目标项添加到链路末尾
      parentChain.push(targetItem);

      // 4. 构建级联数据
      const result = [];
      let currentChildren = result;

      // 遍历父级链构建级联数据
      for (let i = 0; i < parentChain.length; i++) {
        const current = parentChain[i];

        // 获取当前层级的所有兄弟节点
        const siblings = await ctx.model.DictInfo.find({
          typeId: dictType._id,
          parentId: current.parentId,
          enabled: true,
        }).sort({ orderNum: 1 }).lean();

        // 转换为级联格式
        const levelNodes = siblings.map(item => ({
          value: item.value,
          label: item.name,
          children: item.value === current.value ? [] : undefined,
        }));

        // 将当前层级节点添加到对应位置
        if (i === 0) {
          // 第一层直接放入结果数组
          result.push(...levelNodes);
          // 更新当前子节点位置
          const currentNode = result.find(node => node.value === current.value);
          currentChildren = currentNode ? currentNode.children : null;
        } else {
          // 将当前层级添加到父级的children中
          if (currentChildren) {
            currentChildren.push(...levelNodes);
            // 更新当前子节点位置
            const currentNode = currentChildren.find(node => node.value === current.value);
            currentChildren = currentNode ? currentNode.children : null;
          }
        }
      }

      // 5. 如果是最后一级，检查是否还有子节点
      if (currentChildren) {
        const lastChildren = await ctx.model.DictInfo.find({
          typeId: dictType._id,
          parentId: targetItem._id,
          enabled: true,
        }).sort({ orderNum: 1 }).lean();

        if (lastChildren.length > 0) {
          currentChildren.push(...lastChildren.map(item => ({
            value: item.value,
            label: item.name,
          })));
        }
      }

      return result;
    } catch (error) {
      ctx.logger.error('获取字典值层级链失败:', error);
      return [];
    }
  }

  /**
 * 通用字典多级联动选项查询
 * @param {String} key - 字典类型标识（如 'district_code'）
 * @param {String} value - 当前节点的字典 value（如某个地区 zonecode）
 * @return {Array} 每一级的兄弟选项（按层级顺序返回）
 */
  async getDictCascadeOptions(key, value) {
    const { ctx } = this;
    const dictType = await ctx.model.DictType.findOne({ key }).select('_id');
    const typeId = dictType?._id;
    if (!typeId) return [];

    const result = await ctx.model.DictInfo.aggregate([
      {
        $match: {
          typeId,
          value,
        },
      },
      {
        $graphLookup: {
          from: 'dict_infos',
          startWith: '$parentId',
          connectFromField: 'parentId',
          connectToField: '_id',
          as: 'hierarchy',
          depthField: 'level',
        },
      },
      {
        $addFields: {
          hierarchy: {
            $concatArrays: [
              [{ _id: '$_id', parentId: '$parentId', value: '$value', name: '$name', level: -1 }],
              '$hierarchy',
            ],
          },
        },
      },
      { $unwind: '$hierarchy' },
      {
        $lookup: {
          from: 'dict_infos',
          let: { parentId: '$hierarchy.parentId', typeId },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    { $eq: [ '$typeId', '$$typeId' ] },
                    { $eq: [ '$parentId', '$$parentId' ] },
                  ],
                },
              },
            },
            {
              $project: {
                _id: 0,
                value: 1,
                name: 1,
              },
            },
          ],
          as: 'options',
        },
      },
      {
        $sort: { 'hierarchy.level': 1 },
      },
      {
        $project: {
          _id: 0,
          options: 1,
          level: '$hierarchy.level',
          current: {
            value: '$hierarchy.value',
            name: '$hierarchy.name',
          },
          // parentId: '$hierarchy.parentId',
        },
      },
    ]);

    return result;
  }

  /**
 * 获取某节点到顶级的路径（每一级当前节点的 name + value）
 * @param {String} key - 字典类型 key（如 'district_code'）
 * @param {String} value - 当前字典项的 value
 * @return {Array<{ name: string, value: string }>} 路径数组，从顶层到当前
 */
  async getDictNodePath(key, value) {
    const { ctx } = this;
    const DictType = ctx.model.DictType;
    const DictInfo = ctx.model.DictInfo;

    const dictType = await DictType.findOne({ key }).select('_id');
    const typeId = dictType?._id;
    if (!typeId) return [];

    const path = [];

    // 从当前节点开始向上找
    let current = await DictInfo.findOne({ typeId, value }).select('_id name value parentId');

    while (current) {
      path.unshift({ name: current.name, value: current.value });

      if (!current.parentId) break;

      current = await DictInfo.findOne({ typeId, _id: current.parentId }).select('_id name value parentId');
    }

    return path;
  }

  // Redis 键前缀
  get REDIS_KEY_PREFIX() {
    return {
      DICT_CHAIN: 'dict:chain:', // 级联字典缓存前缀
      DICT_TYPE: 'dict:type:',   // 字典类型缓存前缀
      DICT_INFO: 'dict:info:'    // 字典项缓存前缀
    };
  }

  // 获取缓存过期时间（24小时）
  get CACHE_EXPIRE() {
    return 24 * 60 * 60;
  }

  /**
   * 清除字典相关的所有缓存
   * @param {String} typeId - 字典类型ID
   */
  async clearDictCache(typeId) {
    const { ctx, app } = this;
    const redis = app.redis;
    
    // 获取字典类型
    const dictType = await ctx.model.DictType.findById(typeId);
    if (dictType) {
      // 清除该类型的级联缓存
      await redis.del(this.REDIS_KEY_PREFIX.DICT_CHAIN + dictType.key);
    }
  }

  /**
   * 获取级联格式字典项
   * @param {String} key - 字典类型 key（如 'district_code'）
   * @return {Array} 级联格式字典项
   */
  async getChainValue(key) {
    const { ctx, app } = this;
    const redis = app.redis;
    
    // 构造 Redis 键
    const cacheKey = this.REDIS_KEY_PREFIX.DICT_CHAIN + key;
    
    try {
      // 1. 尝试从 Redis 获取缓存
      const cachedData = await redis.get(cacheKey);
      if (cachedData) {
        return JSON.parse(cachedData);
      }
      
      // 2. 缓存不存在，从数据库获取
      const dictType = await ctx.model.DictType.findOne({ key }).select('_id');
      const typeId = dictType?._id;
      if (!typeId) return [];

      // 使用聚合管道获取数据
      const result = await ctx.model.DictInfo.aggregate([
        {
          $match: {
            typeId,
            enabled: true,
          },
        },
        {
          $sort: {
            orderNum: 1,
          },
        },
        {
          $graphLookup: {
            from: 'dict_infos',
            startWith: '$_id',
            connectFromField: '_id',
            connectToField: 'parentId',
            as: 'descendants',
            depthField: 'level',
          },
        },
        {
          $match: {
            parentId: null,
          },
        },
        {
          $addFields: {
            children: {
              $function: {
                body: function (descendants, current) {
                  function buildTree(items, parentId) {
                    return items
                      .filter(
                        (item) => String(item.parentId) === String(parentId)
                      )
                      .map((item) => {
                        const children = buildTree(items, item._id);
                        const node = {
                          value: item.value,
                          label: item.name,
                        };
                        if (children.length > 0) {
                          node.children = children;
                        }
                        return node;
                      })
                      .sort((a, b) => (a.orderNum || 0) - (b.orderNum || 0));
                  }
                  return buildTree(descendants, current);
                },
                args: ['$descendants', '$_id'],
                lang: 'js',
              },
            },
          },
        },
        {
          $project: {
            _id: 0,
            value: '$value',
            label: '$name',
            // 如果 children 是空数组，就移除该字段
            children: {
              $cond: {
                if: { $gt: [{ $size: '$children' }, 0] },
                then: '$children',
                else: '$$REMOVE',
              },
            },
          },
        },
      ]);

      // 3. 将结果存入 Redis
      if (result && result.length > 0) {
        await redis.set(cacheKey, JSON.stringify(result), 'EX', this.CACHE_EXPIRE);
      }

      return result;
    } catch (error) {
      ctx.logger.error('获取级联字典出错:', error);
      return [];
    }
  }
}

module.exports = DictService;
