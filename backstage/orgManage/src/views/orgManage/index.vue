<template>
  <div :class="classObj" class="orgManage">
   <div class="main-container">
    <common-list
    ref="commonList"
    :columns="columns"
    :search-fields="searchFields"
    :form-fields="formFields"
    :form-rules="formRules"
    :actions="['add', 'edit', 'delete', 'view', 'batchDelete', 'info']"
    :fetch-data="fetchData"
    :get-detail="getDetail"
    :pagination-config="{
      pageSizes: [10, 20, 30, 50],
      layout: 'total, sizes, prev, pager, next, jumper',
      pageSize: 10,
      currentPage: 1,
      total: 0
    }"
    @add="handleAdd"
    @edit="handleEdit"
    @delete="handleDelete"
    @batch-delete="handleBatchDelete"
    @size-change="handleSizeChange"
    @current-change="handleCurrentChange"
    @info="handleInfo"
  />
  </div>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
import { initEvent } from "@root/publicMethods/events";
import CommonList from "@/components/CommonList";
import { getOrganizationList, createOrganization, updateOrganization, deleteOrganization, getOrganizationDetail } from '@/api/organization'
import { getDictItemsByKey, getDictChildren, getDictValueChain } from '@/api/dict';
import moment from 'moment'

export default {
  name: "OrgManage",
  data() {
    return {
      sidebarOpened: true,
      device: "desktop",
      searchFields: [
        { 
          label: '机构名称', 
          prop: 'name', 
          type: 'input',
          placeholder: '请输入机构名称'
        },
        {
          label: '所属地区',
          prop: 'zonecode',
          type: 'cascader',
          placeholder: '请选择所属地区',
          props: {
            lazy: true,
            lazyLoad: async (node, resolve) => {
              const { level, value } = node;
              try {
                const key = 'district_code';
                const parentValue = level === 0 ? 'root' : value;
                const res = await getDictChildren({ key, parentValue });
                if (res.status === 200) {
                  resolve(res.data);
                } else {
                  resolve([]);
                }
              } catch (error) {
                console.error('加载地区数据失败:', error);
                resolve([]);
              }
            },
            checkStrictly: true,
            value: 'value',
            label: 'label',
            emitPath: true
          }
        },
        {
          label: '机构级别',
          prop: 'orgLevel',
          type: 'select',
          placeholder: '请选择机构级别',
          options: []
        },
        {
          label: '机构类别',
          prop: 'orgTypes',
          type: 'select',
          placeholder: '请选择机构类别',
          props: {
            multiple: true,
            clearable: true
          },
          options: []
        },
      ],
      columns: [
        { label: '机构名称', prop: 'name' },
        { label: '机构编码', prop: 'orgCode', width: '150' },
        { label: '所属地区', prop: 'zonename' },
        { 
          label: '机构类别', 
          prop: 'orgTypes',
          formatter: (row, column, cellValue) => {
            const options = this.orgTypeOptions.filter(opt => cellValue && cellValue.includes(opt.value));
            return options.map(opt => opt.label).join('、');
          }
        },
        { 
          label: '机构级别', 
          prop: 'orgLevel',
          width: '120',
          formatter: (row, column, cellValue) => {
            const option = this.orgLevelOptions.find(opt => opt.value === cellValue);
            return option ? option.label : cellValue;
          }
        },
        { label: '联系人', prop: 'contactPerson' },
        { label: '联系电话', prop: 'contactPhone' },
        { 
          label: '状态', 
          prop: 'state',
          width: '80',
          formatter: (row, column, cellValue) => {
            return cellValue === '1' ? '正常' : '禁用';
          }
        },
        { 
          label: '更新时间', 
          prop: 'updatedAt',
          width: '140',
          formatter: (row, column, cellValue) => {
            if (!cellValue) return '';
            return moment(cellValue).format('YYYY-MM-DD');
          }
        },
        {
          label: '操作',
          type: 'action',
          width: '240',
          align: 'center',
          fixed: 'right',
          actions: [
            { type: 'view', text: '查看' },
            { type: 'edit', text: '编辑' },
            { type: 'delete', text: '删除', class: 'delete-btn' },
            { type: 'info', text: '详情' }
          ]
        }
      ],
      formFields: [
        { 
          label: '机构名称', 
          prop: 'name', 
          type: 'input',
          placeholder: '请输入机构名称',
          rules: [{ required: true, message: '请输入机构名称', trigger: 'blur' }]
        },
        {
          label: '机构类别',
          prop: 'orgTypes',
          type: 'checkbox',
          placeholder: '请选择机构类别',
          default: [],
          rules: [{ required: true, message: '请选择机构类别', trigger: 'change' }]
        },
        {
          label: '所属地区',
          prop: 'zonecode',
          type: 'cascader',
          props: {
            lazy: true,
            lazyLoad: async (node, resolve) => {
              const { level, value } = node;
              try {
                const key = 'district_code';
                const parentValue = level === 0 ? 'root' : value;
                const res = await getDictChildren({ key, parentValue });
                if (res.status === 200) {
                  resolve(res.data);
                } else {
                  resolve([]);
                }
              } catch (error) {
                console.error('加载地区数据失败:', error);
                resolve([]);
              }
            },
            checkStrictly: true,
            value: 'value',
            label: 'label',
            emitPath: true
          },
          rules: [{ required: true, message: '请选择所属地区', trigger: 'change' }]
        },
        {
          label: '机构级别',
          prop: 'orgLevel',
          type: 'select',
          placeholder: '请选择机构级别',
          options: [],
          rules: [{ required: true, message: '请选择机构级别', trigger: 'change' }]
        },
        {
          label: '联系人',
          prop: 'contactPerson',
          type: 'input',
          placeholder: '请输入联系人姓名',
          rules: [{ required: true, message: '请输入联系人姓名', trigger: 'blur' }]
        },
        {
          label: '联系电话',
          prop: 'contactPhone',
          type: 'input',
          placeholder: '请输入联系电话',
          rules: [
            { required: true, message: '请输入联系电话', trigger: 'blur' },
            {
              validator: (rule, value, callback) => {
                if (!/^(?:(?:\+|00)86)?1[3-9]\d{9}$/.test(value)) {
                  callback(new Error('手机号格式不正确'));
                } else {
                  callback();
                }
              },
              trigger: 'blur'
            }
          ]
        },
        {
          label: '统一社会信用代码',
          prop: 'creditCode',
          type: 'input',
          placeholder: '请输入统一社会信用代码',
          rules: [
            { required: true, message: '请输入统一社会信用代码', trigger: 'blur' },
            {
              validator: (rule, value, callback) => {
                if (!/^(([0-9A-Za-z]{15})|([0-9A-Za-z]{18})|([0-9A-Za-z]{20}))$/.test(value)) {
                  callback(new Error('统一社会信用代码格式不正确'));
                } else {
                  callback();
                }
              },
              trigger: 'blur'
            }
          ]
        },
        {
          label: '状态',
          prop: 'state',
          type: 'radio',
          options: [
            { label: '正常', value: '1' },
            { label: '禁用', value: '0' }
          ],
          default: '1',
          rules: [{ required: true, message: '请选择状态', trigger: 'change' }]
        }
      ],
      formRules: {
        name: [{ required: true, message: '请输入应用名称', trigger: 'blur' }],
        sort: [{ required: true, message: '请输入排序', trigger: 'blur' }],
        status: [{ required: true, message: '请选择状态', trigger: 'blur' }]
      },
      orgLevelOptions: [], // 机构级别选项
      orgTypeOptions: [], // 机构类别选项
    };
  },
  components: {
    CommonList
  },
  computed: {
    ...mapGetters(['appManageFormState', 'appManageList', 'appManageTotal', 'appManageLoading']),
    formState() {
      return this.appManageFormState;
    },
    list() {
      return this.appManageList;
    },
    total() {
      return this.appManageTotal;
    },
    listLoading() {
      return this.appManageLoading;
    },
    classObj() {
      return {
        hideSidebar: !this.sidebarOpened,
        openSidebar: this.sidebarOpened,
        withoutAnimation: "false",
        mobile: this.device === "mobile"
      };
    }
  },
  created() {
    this.initOptions();
  },
  mounted() {
    initEvent(this);
    // this.getList();
  },
  methods: {
    async fetchData(params) {
      try {
        if (params.zonecode && params.zonecode.length > 0) {
          params.zonecode = params.zonecode[params.zonecode.length - 1];
        }
        const queryParams = {
          page: params.page || 1,
          limit: params.limit || 10,
          name: params.name,
          orgLevel: params.orgLevel,
          zonecode: params.zonecode,
          orgTypes: params.orgTypes
        };

        // 移除值为 undefined 或 null 的参数
        Object.keys(queryParams).forEach(key => {
          if (queryParams[key] === undefined || queryParams[key] === null) {
            delete queryParams[key];
          }
        });

        const res = await getOrganizationList(queryParams);
        
        if (res.status === 200) {
          return {
            list: res.data.list,
            total: res.data.total,
            page: Number(params.page),
            limit: Number(params.limit)
          }
        } else {
          this.$message.error(res.message || '获取数据失败')
          return {
            list: [],
            total: 0,
            page: 1,
            limit: 10
          }
        }
      } catch (error) {
        console.error('获取列表失败:', error)
        this.$message.error('获取列表失败')
        return {
          list: [],
          total: 0,
          page: 1,
          limit: 10
        }
      }
    },
    async handleAdd(formData) {
      try {
        // 处理地区编码和地区名称
        if (formData.zonecode && formData.zonecode.length > 0) {
          const zoneValue = formData.zonecode[formData.zonecode.length - 1];
          const res = await getDictValueChain({
            key: 'district_code',
            value: zoneValue
          });
          if (res.status === 200 && res.data && res.data.length > 0) {
            formData.zonecode = zoneValue;
            formData.zonename = res.data[res.data.length - 1].label;
          }
        }
        
        const res = await createOrganization(formData);
        if (res.status === 200) {
          // this.$message.success('添加成功')
          await this.$refs.commonList.loadData()
          return true
        } else {
          this.$message.error(res.message || '添加失败')
          return false
        }
      } catch (error) {
        console.error('添加失败:', error)
        this.$message.error('添加失败')
        return false
      }
    },
    async handleEdit(formData) {
      try {
        // 处理地区编码和地区名称
        if (formData.zonecode && formData.zonecode.length > 0) {
          const zoneValue = formData.zonecode[formData.zonecode.length - 1];
          const res = await getDictValueChain({
            key: 'district_code',
            value: zoneValue
          });
          if (res.status === 200 && res.data && res.data.length > 0) {
            formData.zonecode = zoneValue;
            formData.zonename = res.data[res.data.length - 1].label;
          }
        }
        
        const res = await updateOrganization(formData);
        if (res.status === 200) {
          // this.$message.success('更新成功');
          await this.$refs.commonList.loadData();
          return true;
        }
        this.$message.error(res.message || '更新失败');
        return false;
      } catch (error) {
        console.error('更新失败:', error);
        this.$message.error('更新失败');
        return false;
      }
    },
    async handleDelete(row) {
      if (!row._id) {
        this.$message.error('参数错误');
        return false;
      }
      
      try {
        const res = await deleteOrganization({ id: row._id });
        if (res.status === 200) {
          this.$message.success('删除成功');
          await this.$refs.commonList.loadData();
          return true;
        }
        this.$message.error(res.message || '删除失败');
        return false;
      } catch (error) {
        console.error('删除失败:', error);
        this.$message.error('删除失败');
        return false;
      }
    },
    async handleBatchDelete(rows) {
      if (!rows || !rows.length) {
        this.$message.error('请选择要删除的记录');
        return false;
      }

      try {
        const res = await deleteOrganization({ ids: rows.map(row => row._id) });
        if (res.status === 200) {
          this.$message.success('删除成功');
          await this.$refs.commonList.loadData();
          return true;
        }
        this.$message.error(res.message || '删除失败');
        return false;
      } catch (error) {
        console.error('删除失败:', error);
        this.$message.error('删除失败');
        return false;
      }
    },
    async getDetail(row) {
      try {
        // 1. 获取机构详情
        const detailRes = await getOrganizationDetail({ id: row._id });
        if (detailRes.status !== 200) {
          this.$message.error(detailRes.message || '获取详情失败');
          return {
            status: 500,
            message: '获取详情失败'
          };
        }

        const orgDetail = detailRes.data;

        // 2. 如果有地区编码，获取地区层级链
        if (orgDetail.zonecode) {
          const chainRes = await getDictValueChain({
            key: 'district_code',
            value: orgDetail.zonecode
          });

          if (chainRes.status === 200 && chainRes.data) {
            // 更新级联选择器的选项
            const districtField = this.formFields.find(field => field.prop === 'zonecode');
            if (districtField) {
              // 设置选项数据，同时保持懒加载功能
              this.$set(districtField, 'options', chainRes.data.map(item => ({
                ...item,
                leaf: false,
                children: item.children ? item.children.map(child => ({
                  ...child,
                  leaf: false // 子节点默认可以继续加载
                })) : null // 如果没有子节点则设置为null以触发懒加载
              })));
              
              // 设置选中值为当前值
              orgDetail.zonecode = orgDetail.zonecode;
            }
          }
        }

        return {
          status: 200,
          data: orgDetail
        };
      } catch (error) {
        console.error('获取详情失败:', error);
        this.$message.error('获取详情失败');
        return {
          status: 500,
          message: '获取详情失败'
        };
      }
    },
    handlePagination({ current, pageSize }) {
      this.listQuery.page = current;
      this.listQuery.limit = pageSize;
      this.getList();
    },
    handleSearch(form) {
      this.listQuery = {
        ...this.listQuery,
        ...form
      };
      this.listQuery.page = 1;
      this.getList();
    },
    getList() {
      this.$store.dispatch('appManage/getAppManageList', this.listQuery);
    },
    handleSizeChange(val) {
      this.$refs.commonList.handleSizeChange(val)
    },
    handleCurrentChange(val) {
      this.$refs.commonList.handleCurrentChange(val)
    },
    handleInfo(row) {
      // 跳转到详情页面，可以根据需要修改路由路径
      this.$router.push({
        name: 'OrgInfo',
        query: { id: row._id }
      })
    },
    /**
     * 初始化选项数据
     */
    async initOptions() {
      try {
        // 并行获取机构级别和机构类别选项
        const [levelRes, typeRes] = await Promise.all([
          getDictItemsByKey({ key: 'org_level' }),
          getDictItemsByKey({ key: 'org_type' }),
        ]);

        // 处理机构级别选项
        if (levelRes.status === 200 && levelRes.data) {
          this.orgLevelOptions = levelRes.data.map(item => ({
            label: item.name,
            value: item.value
          }));
          // 更新搜索字段和表单字段的选项
          this.searchFields.find(field => field.prop === 'orgLevel').options = this.orgLevelOptions;
          this.formFields.find(field => field.prop === 'orgLevel').options = this.orgLevelOptions;
        }

        // 处理机构类别选项
        if (typeRes.status === 200 && typeRes.data) {
          this.orgTypeOptions = typeRes.data.map(item => ({
            label: item.name,
            value: item.value
          }));
          // 更新表单字段的选项
          // const orgTypesField = this.formFields.find(field => field.prop === 'orgTypes');
          // if (orgTypesField) {
          //   orgTypesField.options = this.orgTypeOptions;
          // }
          // 更新搜索字段选项
          this.searchFields.find(field => field.prop === 'orgTypes').options = this.orgTypeOptions;
          this.formFields.find(field => field.prop === 'orgTypes').options = this.orgTypeOptions;
          console.log(this.formFields, 222222222);
        }
      } catch (error) {
        console.error('初始化选项数据失败:', error);
        this.$message.error('初始化选项数据失败');
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.orgManage {
  height: 100%;
  .main-container {
    // height: 100%;
    // padding: 20px;
    // background-color: #f0f2f5;
    .app-wrapper {
      height: 100%;
      .app-card {
        background: #fff;
        border-radius: 4px;
        box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
        
        .search-title {
          position: relative;
          height: 50px;
          line-height: 50px;
          padding: 0 20px;
          border-bottom: 1px solid #f0f0f0;
          
          span {
            font-size: 14px;
            font-weight: 500;
            color: #333;
            margin-left: 12px;
          }
          
          &::before {
            content: '';
            position: absolute;
            left: 20px;
            top: 50%;
            transform: translateY(-50%);
            width: 4px;
            height: 16px;
            background: #409EFF;
            border-radius: 0 2px 2px 0;
          }
        }

        .table-title {
          position: relative;
          height: 50px;
          padding: 0 20px;
          margin-top: 16px;
          
          .title-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            height: 100%;
            
            span {
              font-size: 14px;
              font-weight: 500;
              color: #333;
              margin-left: 12px;
              position: relative;
              
              &::before {
                content: '';
                position: absolute;
                left: -12px;
                top: 50%;
                transform: translateY(-50%);
                width: 4px;
                height: 16px;
                background: #409EFF;
                border-radius: 0 2px 2px 0;
              }
            }
            
            .button-group {
              display: flex;
              gap: 10px;
            }
          }
        }

        .pagination-container {
          padding: 20px;
        }
      }
    }
  }
}
</style>
